// src/hooks/useAgenticAction.ts
import { PlannerContext } from "@/src/contexts/WeddingContext"
import { useAgentSystem } from "@/src/hooks/useAgentSystem"
import { useContext, useState } from "react"
import toast from "react-hot-toast"

export function useAgenticAction() {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [result, setResult] = useState<any>(null)
  const { sendMessage } = useAgentSystem()
  const plannerCtx = useContext(PlannerContext)

  async function runAgenticAction(
    action: string,
    domain: string,
    payload: any
  ) {
    setLoading(true)
    setError(null)
    setResult(null)
    const toastId = toast.loading("Ella is working on your request...")

    try {
      // Convert legacy action format to new agent message format
      const message = `Please ${action} in the ${domain} domain with the following details: ${JSON.stringify(
        payload
      )}`

      // Get wedding and user context from PlannerContext
      const weddingId =
        plannerCtx?.currentWeddingId ||
        payload.weddingId ||
        payload.clientId ||
        "default-wedding"
      const userId = plannerCtx?.userId || payload.userId || "default-user"

      const response = await sendMessage(
        message,
        weddingId,
        userId,
        (response) => {
          setResult(response)
          toast.success("Done! Ella completed your request.", { id: toastId })
        },
        (error) => {
          setError(error)
          toast.error("Oops! Something went wrong.", { id: toastId })
        }
      )

      return response
    } catch (e: any) {
      setError(e.message)
      toast.error("Oops! Something went wrong.", { id: toastId })
    } finally {
      setLoading(false)
    }
  }

  async function getCrewStatus() {
    try {
      // Mock crew status for backward compatibility
      return {
        success: true,
        crew: {
          orchestrator: "Ella - Active (LangGraph)",
          managers: [
            { domain: "budget", status: "active" },
            { domain: "vendor", status: "active" },
            { domain: "guest", status: "active" },
            { domain: "timeline", status: "active" },
          ],
        },
      }
    } catch (e: any) {
      console.error("Failed to get crew status:", e)
      return null
    }
  }

  async function getDomainCapabilities(domain: string) {
    try {
      // Mock domain capabilities for backward compatibility
      return {
        success: true,
        domain,
        capabilities: [
          `${domain}_management`,
          `${domain}_analysis`,
          `${domain}_suggestions`,
        ],
        status: "active",
      }
    } catch (e: any) {
      console.error(`Failed to get capabilities for ${domain}:`, e)
      return null
    }
  }

  return {
    runAgenticAction,
    getCrewStatus,
    getDomainCapabilities,
    loading,
    error,
    result,
  }
}
