import { PlannerContext } from "@/src/contexts/WeddingContext"
import { AppMode } from "@/src/types/index"
import { useContext } from "react"

/**
 * Hook to get the current app mode for AI generation
 * Returns the actual app mode from context
 */
export const useAppMode = (): AppMode => {
  const plannerCtx = useContext(PlannerContext)

  // Return the actual app mode from context, fallback to 'dev' if not available
  return plannerCtx?.appMode || "dev"
}

export default useAppMode
