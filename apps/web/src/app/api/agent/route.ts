import { performanceAnalytics } from "@agent-system/analytics/performanceAnalytics"
import { supabaseService } from "@agent-system/memory/supabaseClient"
import { createWeddingPlannerGraph } from "@agent-system/orchestratorGraph"
import { HumanMessage } from "@langchain/core/messages"
import { NextRequest, NextResponse } from "next/server"

export const runtime = "edge"

interface AgentRequest {
  message: string
  weddingId: string
  userId: string
  threadId?: string
}

export async function POST(request: NextRequest) {
  const encoder = new TextEncoder()

  const stream = new ReadableStream({
    async start(controller) {
      try {
        const body: AgentRequest = await request.json()

        // Validate required fields
        if (!body.message || !body.weddingId || !body.userId) {
          controller.enqueue(
            encoder.encode(
              'data: {"error": "Missing required fields: message, weddingId, userId"}\n\n'
            )
          )
          controller.close()
          return
        }

        // TODO: Add authentication validation here
        // For now, we'll proceed without strict auth to fix the integration
        // In production, validate the user's session/token

        // Create a unique run ID for this conversation
        const runId = crypto.randomUUID()
        const startTime = Date.now()

        // Record the agent run start
        await supabaseService.from("agent_runs").insert({
          id: runId,
          wedding_id: body.weddingId,
          user_id: body.userId,
          agent_type: "orchestrator",
          status: "running",
          input_data: {
            message: body.message,
            threadId: body.threadId,
          },
        })

        // Send initial response
        controller.enqueue(
          encoder.encode(
            'data: {"type": "start", "runId": "' + runId + '"}\n\n'
          )
        )

        // Create and compile the wedding planner graph
        const graph = createWeddingPlannerGraph()
        const compiledGraph = graph.compile()

        // Prepare the initial state
        const initialState = {
          messages: [new HumanMessage(body.message)],
          weddingId: body.weddingId,
          userId: body.userId,
          runId: runId,
          currentAgent: "orchestrator",
          context: {},
          requiresConfirmation: false,
          confirmationData: {},
          workflowId: undefined,
          coordinationId: undefined,
          activeWorkers: [],
          performanceMetrics: {},
        }

        // Stream the agent execution
        let finalResponse = ""
        let agentType = "orchestrator"

        try {
          // Execute the graph
          const result = await compiledGraph.invoke(initialState)

          // Extract the final response
          if (result.messages && result.messages.length > 0) {
            const lastMessage = result.messages[result.messages.length - 1]
            finalResponse =
              typeof lastMessage.content === "string"
                ? lastMessage.content
                : "I apologize, but I encountered an issue processing your request."
            agentType = result.currentAgent || "orchestrator"
          }

          // Stream the response
          controller.enqueue(
            encoder.encode(
              'data: {"type": "response", "content": ' +
                JSON.stringify(finalResponse) +
                ', "agent": "' +
                agentType +
                '"}\n\n'
            )
          )

          // Record successful completion
          await supabaseService
            .from("agent_runs")
            .update({
              status: "completed",
              output_data: {
                response: finalResponse,
                agent_type: agentType,
                execution_time: Date.now() - startTime,
              },
              completed_at: new Date().toISOString(),
            })
            .eq("id", runId)

          // Record performance metrics
          await performanceAnalytics.recordMetric({
            agentType: agentType,
            metricName: "response_time",
            metricValue: Date.now() - startTime,
            metricUnit: "milliseconds",
            timePeriod: "hour",
            periodStart: new Date(Date.now() - 60 * 60 * 1000),
            periodEnd: new Date(),
            metadata: {
              runId: runId,
              weddingId: body.weddingId,
              success: true,
            },
          })
        } catch (error) {
          console.error("Error executing agent graph:", error)

          const errorMessage =
            "I apologize, but I encountered an issue processing your request. Please try again."
          controller.enqueue(
            encoder.encode(
              'data: {"type": "error", "content": ' +
                JSON.stringify(errorMessage) +
                '"}\n\n'
            )
          )

          // Record failed completion
          await supabaseService
            .from("agent_runs")
            .update({
              status: "failed",
              output_data: {
                error: error instanceof Error ? error.message : "Unknown error",
                execution_time: Date.now() - startTime,
              },
              completed_at: new Date().toISOString(),
            })
            .eq("id", runId)

          // Record error metrics
          await performanceAnalytics.recordMetric({
            agentType: agentType,
            metricName: "error_rate",
            metricValue: 1,
            metricUnit: "count",
            timePeriod: "hour",
            periodStart: new Date(Date.now() - 60 * 60 * 1000),
            periodEnd: new Date(),
            metadata: {
              runId: runId,
              weddingId: body.weddingId,
              error: error instanceof Error ? error.message : "Unknown error",
            },
          })
        }

        // Send completion signal
        controller.enqueue(encoder.encode('data: {"type": "complete"}\n\n'))
        controller.close()
      } catch (error) {
        console.error("Error in agent API:", error)
        controller.enqueue(
          encoder.encode('data: {"error": "Internal server error"}\n\n')
        )
        controller.close()
      }
    },
  })

  return new Response(stream, {
    headers: {
      "Content-Type": "text/plain; charset=utf-8",
      "Cache-Control": "no-cache",
      Connection: "keep-alive",
    },
  })
}

export async function GET() {
  return NextResponse.json({
    message: "VL Wedding Planner Agent API",
    version: "2.0.0",
    features: [
      "LangGraph-powered orchestration",
      "Multi-domain agent coordination",
      "Associate worker delegation",
      "Learning and feedback system",
      "Performance analytics",
      "Cross-domain workflows",
    ],
  })
}

export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      "Access-Control-Allow-Origin": "*",
      "Access-Control-Allow-Methods": "GET, POST, OPTIONS",
      "Access-Control-Allow-Headers": "Content-Type",
    },
  })
}
