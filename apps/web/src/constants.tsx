import {
  BriefcaseIcon,
  BuildingStorefrontIcon,
  CalendarDaysIcon,
  ChatBubbleLeftEllipsisIcon,
  CheckBadgeIcon,
  ClipboardDocumentIcon,
  ClipboardIcon,
  CogIcon,
  CpuChipIcon,
  CurrencyDollarIcon,
  HomeIcon,
  SparklesIcon,
  UserCircleIcon,
  UserGroupIcon,
  UsersIcon
} from '@/src/components/icons/HeroIcons';

import { LLMModel, NavItemType, PredefinedAgent } from '@/src/types/index';

// For when a specific wedding is selected
export const NAV_ITEMS: NavItemType[] = [
  { id: 'overview', label: 'Wedding Overview', path: 'overview', icon: HomeIcon },
  { id: 'visionBoard', label: 'Vision Board', path: 'vision-board', icon: ClipboardIcon },
  { id: 'timeline', label: 'Timeline', path: 'timeline', icon: CalendarDaysIcon },
  { id: 'priorityTasks', label: 'Tasks', path: 'priority-tasks', icon: CheckBadgeIcon },
  { id: 'budget', label: 'Budget', path: 'budget', icon: CurrencyDollarI<PERSON> },
  { id: 'guests', label: 'Guests', path: 'guests', icon: UsersIcon },
  { id: 'vendors', label: 'Vendors', path: 'vendors', icon: BuildingStorefrontIcon },
  { id: 'lookGenerator', label: 'Look Generator', path: 'look-generator', icon: SparklesIcon },
  { id: 'weddingParty', label: 'Wedding Party', path: 'wedding-party', icon: UserGroupIcon },
  { id: 'aiSystem', label: 'AI Agents', path: 'ai-agent-system', icon: CpuChipIcon },
];

// For the main planner dashboard / global views
export const PLANNER_NAV_ITEMS: NavItemType[] = [
  { id: 'plannerDashboard', label: 'Planner Dashboard', path: '/dashboard', icon: BriefcaseIcon },
  { id: 'agentDashboard', label: 'AI Agents', path: '/agent-dashboard', icon: CpuChipIcon },
  { id: 'dataSources', label: 'Data Sources', path: '/data-sources', icon: ClipboardDocumentIcon },
  { id: 'plannerSettings', label: 'Global Settings', path: '/settings', icon: CogIcon },
  { id: 'plannerProfile', label: 'Planner Profile', path: '/user-profile', icon: UserCircleIcon },
];


// These will be dynamically generated by AI services
export const ELLA_AVATAR_URL = "data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=";
export const USER_AVATAR_URL = "data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=";

export const DEFAULT_WEDDING_PHOTO_URL = "data:image/gif;base64,R0lGODlhAQABAAD/ACwAAAAAAQABAAACADs=";

export const AVAILABLE_LLM_MODELS: LLMModel[] = [
  'gemini-2.5-flash-preview-05-20',
  'imagen-3.0-generate-002',
  'gpt-4.1',
  'claude-sonnet-4-0',
];

// Predefined AI Agents with Hierarchy and LLM Model
export const PREDEFINED_AGENTS: PredefinedAgent[] = [
  // Tier 1: Orchestrator
  {
    id: 'ellaOrchestrator',
    name: 'Ella - AI Orchestrator',
    description: 'The primary AI assistant coordinating all planning activities and other agents.',
    icon: SparklesIcon,
    defaultSystemInstruction: "You are Ella, the master AI wedding orchestrator. You manage all aspects of wedding planning, delegate tasks to specialized sub-agents, and provide comprehensive support to the planner. Your goal is to ensure a seamless and successful planning process for each client.",
    agentType: 'orchestrator',
    defaultLlmModel: 'gemini-2.5-flash-preview-05-20',
    capabilities: ['Task delegation', 'Decision making', 'Client communication', 'Workflow orchestration'],
    estimatedCostPerTask: 0.15,
    // connectedDataSourceIds: [] // Initialize in AgentConfig via context
  },
  // Tier 2: Manager Agents (reporting to Ella)
  {
    id: 'venueManagerAI',
    name: 'Venue Manager AI',
    description: 'Oversees venue research, shortlisting, and related tasks.',
    icon: BuildingStorefrontIcon,
    defaultSystemInstruction: "You are a specialized AI Venue Manager. Your role is to oversee all venue-related activities, manage Venue Scout Associates, and present synthesized venue options to the planner.",
    agentType: 'manager',
    parentId: 'ellaOrchestrator',
    defaultLlmModel: 'gemini-2.5-flash-preview-05-20',
    // connectedDataSourceIds: []
  },
  {
    id: 'budgetManagerAI',
    name: 'Budget Manager AI',
    description: 'Manages client budgets, tracks expenses, and oversees budget optimization.',
    icon: CurrencyDollarIcon,
    defaultSystemInstruction: "You are an AI Budget Manager. You assist the planner in creating, tracking, and managing client wedding budgets, and manage Budget Analyst Associates.",
    agentType: 'manager',
    parentId: 'ellaOrchestrator',
    defaultLlmModel: 'gemini-2.5-flash-preview-05-20',
    capabilities: ['Budget tracking', 'Expense analysis', 'Cost optimization', 'Financial reporting'],
    estimatedCostPerTask: 0.08,
    // connectedDataSourceIds: []
  },
  {
    id: 'timelineManagerAI',
    name: 'Timeline Manager AI',
    description: 'Creates and manages wedding timelines and task schedules.',
    icon: CalendarDaysIcon,
    defaultSystemInstruction: "You are an AI Timeline Manager. Your responsibility is to develop and maintain detailed wedding planning timelines and task lists, delegating to Scheduling Associates.",
    agentType: 'manager',
    parentId: 'ellaOrchestrator',
    defaultLlmModel: 'gemini-2.5-flash-preview-05-20',
    // connectedDataSourceIds: []
  },
  {
    id: 'guestManagerAI',
    name: 'Guest Manager AI',
    description: 'Helps manage client guest lists, RSVPs, and related communications.',
    icon: UsersIcon,
    defaultSystemInstruction: "You are an AI Guest Manager. You aid in organizing client guest lists, tracking RSVPs, and managing Guest Data Associates.",
    agentType: 'manager',
    parentId: 'ellaOrchestrator',
    defaultLlmModel: 'gemini-2.5-flash-preview-05-20',
    // connectedDataSourceIds: []
  },
  // Tier 3: Associate Agents (reporting to specific Managers)
  // Venue Associates
  {
    id: 'venueScoutAssociate',
    name: 'Venue Scout Associate',
    description: 'Performs detailed venue research and data gathering.',
    icon: BuildingStorefrontIcon,
    defaultSystemInstruction: "You are a Venue Scout Associate. Your task is to find wedding venues based on specific criteria (location, capacity, style, budget) provided by the Venue Manager AI. Collect details on pricing, availability, and amenities.",
    agentType: 'associate',
    parentId: 'venueManagerAI',
    defaultLlmModel: 'gemini-2.5-flash-preview-05-20',
    // connectedDataSourceIds: []
  },
  {
    id: 'venueAvailabilityCheckerAssociate',
    name: 'Venue Availability Checker',
    description: 'Checks real-time availability for shortlisted venues.',
    icon: CheckBadgeIcon,
    defaultSystemInstruction: "You are a Venue Availability Checker Associate. Contact shortlisted venues (simulated) to confirm availability for specific dates provided by the Venue Manager AI.",
    agentType: 'associate',
    parentId: 'venueManagerAI',
    defaultLlmModel: 'gemini-2.5-flash-preview-05-20',
    // connectedDataSourceIds: []
  },
  // Budget Associates
  {
    id: 'budgetDataAnalystAssociate',
    name: 'Budget Data Analyst',
    description: 'Analyzes spending patterns and identifies potential savings.',
    icon: CurrencyDollarIcon,
    defaultSystemInstruction: "You are a Budget Data Analyst Associate. Analyze budget data provided by the Budget Manager AI to identify spending trends, potential overruns, and cost-saving opportunities.",
    agentType: 'associate',
    parentId: 'budgetManagerAI',
    defaultLlmModel: 'gemini-2.5-flash-preview-05-20',
    // connectedDataSourceIds: []
  },
  // Styling Assistant (can be a Manager or an Associate depending on complexity)
  {
    id: 'stylingAssistantAI',
    name: 'Styling Assistant AI',
    description: 'Generates look ideas and styling suggestions for client weddings. Uses image generation models.',
    icon: SparklesIcon,
    defaultSystemInstruction: "You are an AI Styling Assistant. You generate visual ideas and suggestions for wedding attire, decor, and overall aesthetics based on client preferences and vision board themes. You primarily use image generation models.",
    agentType: 'manager', // Could also be 'associate' reporting to an "Experience Manager"
    parentId: 'ellaOrchestrator',
    defaultLlmModel: 'imagen-3.0-generate-002', // Default to image model
    capabilities: ['Image generation', 'Style recommendations', 'Color coordination', 'Trend analysis'],
    estimatedCostPerTask: 0.25,
    // connectedDataSourceIds: []
  },
  {
    id: 'clientCommsAI',
    name: 'Client Comms AI',
    description: 'Assists in drafting communications to clients.',
    icon: ChatBubbleLeftEllipsisIcon,
    defaultSystemInstruction: "You are an AI Client Communications assistant. You help the planner draft professional and empathetic emails, updates, and reminders for their clients. Focus on clarity, tone, and conciseness.",
    agentType: 'manager',
    parentId: 'ellaOrchestrator',
    defaultLlmModel: 'gemini-2.5-flash-preview-05-20',
    // connectedDataSourceIds: []
  },
];
