import Button from '@/src/components/common/Button';
import Card from '@/src/components/common/Card';
import LoadingSpinner from '@/src/components/common/LoadingSpinner';
import PageTitle from '@/src/components/common/PageTitle';
import Textarea from '@/src/components/common/Textarea';
import { CpuChipIcon, SparklesIcon } from '@/src/components/icons/HeroIcons';
import { AVAILABLE_LLM_MODELS } from '@/src/constants';
import { useToast } from '@/src/contexts/ToastContext';
import { PlannerContext } from '@/src/contexts/WeddingContext';
import { AgentConfig, AgentWeddingOverride, LLMModel } from '@/src/types/index';
import React, { useContext, useEffect, useState } from 'react';

const AgentDashboardPage: React.FC = () => {
  const plannerCtx = useContext(PlannerContext);
  const { addToast } = useToast();

  const [selectedClientWeddingId, setSelectedClientWeddingId] = useState<string | null>(null);
  const [editableAgentConfigs, setEditableAgentConfigs] = useState<AgentConfig[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    if (plannerCtx?.agentConfigurations) {
      const newEditableConfigs = plannerCtx.agentConfigurations.map(agent => ({
        ...agent,
        weddingOverrides: agent.weddingOverrides ? JSON.parse(JSON.stringify(agent.weddingOverrides)) : {},
        connectedDataSourceIds: Array.isArray(agent.connectedDataSourceIds) ? [...agent.connectedDataSourceIds] : [], // Ensure it's an array
      }));
      setEditableAgentConfigs(newEditableConfigs);
    }
  }, [plannerCtx?.agentConfigurations]);

  const handleConfigChange = (
    agentId: string,
    field: keyof Omit<AgentConfig, 'id' | 'name' | 'description' | 'icon' | 'agentType' | 'parentId' | 'weddingOverrides' | 'defaultSystemInstruction' | 'defaultLlmModel' | 'connectedDataSourceIds'>,
    value: any
  ) => {
    setEditableAgentConfigs(prev =>
      prev.map(agent =>
        agent.id === agentId ? { ...agent, [field]: value } : agent
      )
    );
  };

  const handleGlobalSystemInstructionChange = (agentId: string, value: string) => {
    setEditableAgentConfigs(prev =>
      prev.map(agent =>
        agent.id === agentId ? { ...agent, defaultSystemInstruction: value } : agent
      )
    );
  };

  const handleWeddingOverrideChange = (
    agentId: string,
    weddingId: string,
    field: keyof AgentWeddingOverride,
    value: string | boolean
  ) => {
    setEditableAgentConfigs(prev =>
      prev.map(agent => {
        if (agent.id === agentId) {
          const newOverrides = { ...agent.weddingOverrides };
          newOverrides[weddingId] = {
            ...(newOverrides[weddingId] || { isActiveForWedding: agent.isActiveGlobal, systemInstructionOverride: '' }),
            [field]: value,
          };
          return { ...agent, weddingOverrides: { ...newOverrides } };
        }
        return agent;
      })
    );
  };

  const handleDataSourceConnectionChange = (agentId: string, selectedDataSourceIds: string[]) => {
    setEditableAgentConfigs(prev =>
      prev.map(agent =>
        agent.id === agentId ? { ...agent, connectedDataSourceIds: selectedDataSourceIds } : agent
      )
    );
  };

  const handleSaveAllConfigurations = () => {
    if (!plannerCtx) return;
    setIsLoading(true);
    editableAgentConfigs.forEach(config => {
      const finalConnectedDataSourceIds = Array.isArray(config.connectedDataSourceIds) ? config.connectedDataSourceIds : [];
      const updatePayload: Partial<Omit<AgentConfig, 'id' | 'name' | 'description' | 'icon' | 'agentType' | 'parentId' | 'defaultLlmModel'>> = {
        isActiveGlobal: config.isActiveGlobal,
        llmModel: config.llmModel,
        defaultSystemInstruction: config.defaultSystemInstruction,
        weddingOverrides: config.weddingOverrides,
        connectedDataSourceIds: finalConnectedDataSourceIds,
      };
      plannerCtx.updateAgentConfiguration(config.id, updatePayload);
    });
    setIsLoading(false);
    addToast("All agent configurations saved successfully!", "success");
  };

  const renderAgentNode = (agent: AgentConfig, allAgents: AgentConfig[], level: number) => {
    const IconComponent = agent.icon;
    if (!IconComponent || typeof IconComponent !== 'function') {
      console.error(`Agent ${agent.id} (${agent.name}) has an invalid icon component.`);
      return <div style={{ marginLeft: `${level * 20}px` }} className="p-4 rounded-lg mb-4 bg-destructive/20 text-destructive">Error: Agent icon for &quot;{agent.name}&quot; is missing or invalid.</div>;
    }
    const children = allAgents.filter(child => child.parentId === agent.id);
    const availableDataSources = plannerCtx?.dataSources.filter(ds => ds.status === 'Ready') || [];

    return (
      <div key={agent.id} style={{ marginLeft: `${level * 20}px` }} className={`p-4 rounded-lg mb-4 ${level > 0 ? 'bg-secondary/30' : 'bg-card border border-border'}`}>
        <div className="flex items-center mb-3">
          <IconComponent className="w-8 h-8 mr-3 text-primary flex-shrink-0" />
          <div>
            <h4 className="text-lg font-semibold text-foreground">{agent.name}</h4>
            <p className="text-xs text-muted-foreground">Type: <span className="capitalize">{agent.agentType}</span> | Model: {agent.llmModel}</p>
          </div>
        </div>
        <p className="text-sm text-muted-foreground mb-4">{agent.description}</p>

        <div className="space-y-4 border-t border-border pt-4">
          <h5 className="text-md font-semibold text-primary">Global Configuration</h5>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-end">
            <div>
              <label htmlFor={`llmModel-${agent.id}`} className="block text-sm font-medium text-muted-foreground mb-1">LLM Model</label>
              <select
                id={`llmModel-${agent.id}`}
                name="llmModel"
                value={agent.llmModel} // Use agent.llmModel from editable state
                onChange={(e) => handleConfigChange(agent.id, 'llmModel', e.target.value as LLMModel)}
                className="w-full px-3 py-2 rounded-md bg-input border border-input-border text-foreground focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
              >
                {AVAILABLE_LLM_MODELS.map(model => (
                  <option key={model} value={model} disabled={agent.defaultLlmModel === 'imagen-3.0-generate-002' && model !== 'imagen-3.0-generate-002'}>
                    {model} {agent.defaultLlmModel === 'imagen-3.0-generate-002' && model !== 'imagen-3.0-generate-002' ? '(Image model preferred)' : ''}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id={`isActiveGlobal-${agent.id}`}
                name="isActiveGlobal"
                checked={agent.isActiveGlobal}
                onChange={(e) => handleConfigChange(agent.id, 'isActiveGlobal', e.target.checked)}
                className="h-5 w-5 text-primary bg-input border-border rounded focus:ring-primary mr-2"
              />
              <label htmlFor={`isActiveGlobal-${agent.id}`} className="text-sm text-muted-foreground">Active Globally</label>
            </div>
          </div>
          <Textarea
            label="Default System Instruction"
            value={agent.defaultSystemInstruction}
            onChange={(e) => handleGlobalSystemInstructionChange(agent.id, e.target.value)}
            rows={4}
            className="text-sm"
          />

          {/* Connected Data Sources */}
          <div>
            <label htmlFor={`dataSources-${agent.id}`} className="block text-sm font-medium text-muted-foreground mb-1">Connected Data Sources (Global)</label>
            <select
              id={`dataSources-${agent.id}`}
              multiple
              value={agent.connectedDataSourceIds || []}
              onChange={(e) => {
                const selectedIds = Array.from(e.target.selectedOptions, option => option.value);
                handleDataSourceConnectionChange(agent.id, selectedIds);
              }}
              className="w-full px-3 py-2 rounded-md bg-input border border-input-border text-foreground focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary h-32 custom-scrollbar"
            >
              {availableDataSources.length === 0 && <option disabled>No data sources available</option>}
              {availableDataSources.map(ds => (
                <option key={ds.id} value={ds.id}>{ds.name} ({ds.type})</option>
              ))}
            </select>
            {agent.connectedDataSourceIds && agent.connectedDataSourceIds.length > 0 && (
              <div className="mt-2 text-xs text-muted-foreground">
                Currently connected: {agent.connectedDataSourceIds.map(id => availableDataSources.find(ds => ds.id === id)?.name || 'Unknown').join(', ')}
              </div>
            )}
          </div>


          {selectedClientWeddingId && plannerCtx?.managedWeddings.find(w => w.id === selectedClientWeddingId) && (
            <div className="mt-4 pt-4 border-t border-dashed border-border">
              <h5 className="text-md font-semibold text-primary mb-2">Overrides for: <span className="text-foreground">{plannerCtx.managedWeddings.find(w => w.id === selectedClientWeddingId)?.weddingDetails.coupleNames}</span></h5>
              <div className="flex items-center mb-3">
                <input
                  type="checkbox"
                  id={`isActiveForWedding-${agent.id}-${selectedClientWeddingId}`}
                  checked={agent.weddingOverrides[selectedClientWeddingId]?.isActiveForWedding ?? agent.isActiveGlobal}
                  onChange={(e) => handleWeddingOverrideChange(agent.id, selectedClientWeddingId!, 'isActiveForWedding', e.target.checked)}
                  className="h-5 w-5 text-primary bg-input border-border rounded focus:ring-primary mr-2"
                />
                <label htmlFor={`isActiveForWedding-${agent.id}-${selectedClientWeddingId}`} className="text-sm text-muted-foreground">Active for this Wedding</label>
              </div>
              <Textarea
                label="System Instruction Override (Optional)"
                value={agent.weddingOverrides[selectedClientWeddingId]?.systemInstructionOverride || ''}
                onChange={(e) => handleWeddingOverrideChange(agent.id, selectedClientWeddingId!, 'systemInstructionOverride', e.target.value)}
                rows={3}
                placeholder="Leave blank to use default instruction."
                className="text-sm"
                disabled={!(agent.weddingOverrides[selectedClientWeddingId]?.isActiveForWedding ?? agent.isActiveGlobal)}
              />
            </div>
          )}
        </div>

        {children.length > 0 && (
          <div className="mt-4 pt-4 border-t border-dashed border-primary/30">
            <h5 className="text-sm font-semibold text-muted-foreground mb-2">Child Agents:</h5>
            {children.map(childAgent => renderAgentNode(childAgent, allAgents, level + 1))}
          </div>
        )}
      </div>
    );
  };

  if (!plannerCtx || (editableAgentConfigs.length === 0 && plannerCtx.agentConfigurations.length > 0)) {
    return <LoadingSpinner text="Loading AI Agent Dashboard..." />;
  }
  if (!plannerCtx || plannerCtx.agentConfigurations.length === 0) {
    return (
      <div className="space-y-6">
        <PageTitle title="AI Agents" subtitle="Manage your AI assistant team, their roles, models, and instructions." icon={<CpuChipIcon />} />
        <Card>
          <p className="text-muted-foreground text-center py-8">No AI agents have been defined in the system constants.</p>
        </Card>
      </div>
    );
  }

  const topLevelAgents = editableAgentConfigs.filter(agent => !agent.parentId);

  return (
    <div className="space-y-6">
      <PageTitle title="AI Agents" subtitle="Manage your LangGraph-powered AI assistant team, their roles, models, and configurations." icon={<CpuChipIcon />} />

      {/* Agent System Status */}
      <Card title="LangGraph Agent System Status">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="p-4 bg-primary/10 rounded-lg">
            <h4 className="font-medium text-primary">Orchestrator</h4>
            <p className="text-sm text-muted-foreground mt-1">Ella - CEO Agent</p>
            <p className="text-xs text-muted-foreground mt-2">Status: Active</p>
          </div>
          <div className="p-4 bg-secondary/10 rounded-lg">
            <h4 className="font-medium text-secondary">Manager Agents</h4>
            <p className="text-sm text-muted-foreground mt-1">6 Domain Specialists</p>
            <p className="text-xs text-muted-foreground mt-2">Budget, Vendor, Guest, Timeline, Vision, Styling</p>
          </div>
          <div className="p-4 bg-accent/10 rounded-lg">
            <h4 className="font-medium text-accent">Worker Agents</h4>
            <p className="text-sm text-muted-foreground mt-1">Dynamic Spawning</p>
            <p className="text-xs text-muted-foreground mt-2">Created as needed for specific tasks</p>
          </div>
        </div>
        <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <p className="text-sm text-blue-800">
            <strong>LangGraph Integration:</strong> The agent system uses LangGraph for stateful workflows,
            memory persistence, and human-in-the-loop capabilities. Agents can be configured per wedding
            or use global defaults.
          </p>
        </div>
      </Card>

      <Card>
        <div className="flex flex-col sm:flex-row justify-between items-center mb-6 gap-4">
          <div>
            <label htmlFor="clientWeddingSelect" className="block text-sm font-medium text-muted-foreground mb-1">Configure for Specific Client (Optional)</label>
            <select
              id="clientWeddingSelect"
              value={selectedClientWeddingId || ''}
              onChange={(e) => setSelectedClientWeddingId(e.target.value || null)}
              className="w-full sm:min-w-[300px] px-3 py-2 rounded-md bg-input border border-input-border text-foreground focus:outline-none focus:ring-1 focus:ring-primary focus:border-primary"
            >
              <option value="">Global Configurations</option>
              {plannerCtx.managedWeddings.map(wedding => (
                <option key={wedding.id} value={wedding.id}>
                  {wedding.weddingDetails.coupleNames} (ID: ...{wedding.id.slice(-4)})
                </option>
              ))}
            </select>
          </div>
          <Button
            onClick={handleSaveAllConfigurations}
            variant="primary"
            size="lg"
            isLoading={isLoading}
            disabled={isLoading}
            className="w-full sm:w-auto mt-4 sm:mt-0"
            leftIcon={<SparklesIcon className="w-5 h-5" />}
          >
            Save All Configurations
          </Button>
        </div>
      </Card>

      {topLevelAgents.length === 0 && editableAgentConfigs.length > 0 && <p className="text-muted-foreground">Loading agent hierarchy or no top-level agents found...</p>}
      {editableAgentConfigs.length > 0 ?
        topLevelAgents.map(agent => renderAgentNode(agent, editableAgentConfigs, 0))
        : <p className="text-muted-foreground text-center py-8">No AI agents loaded or defined.</p>
      }
    </div>
  );
};

export default AgentDashboardPage;
