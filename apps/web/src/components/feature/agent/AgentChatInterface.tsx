import React, { useState, useContext, useRef, useEffect } from 'react';
import { PlannerContext } from '@/src/contexts/WeddingContext';
import { useAgentSystem } from '@/src/hooks/useAgentSystem';
import Button from '@/src/components/common/Button';
import Input from '@/src/components/common/Input';
import Card from '@/src/components/common/Card';
import { PaperAirplaneIcon, SparklesIcon } from '@/src/components/icons/HeroIcons';

interface ChatMessage {
  id: string;
  content: string;
  sender: 'user' | 'agent';
  timestamp: Date;
  agent?: string;
}

const AgentChatInterface: React.FC = () => {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputMessage, setInputMessage] = useState('');
  const { sendMessage, isLoading } = useAgentSystem();
  const plannerCtx = useContext(PlannerContext);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || isLoading) return;
    
    if (!plannerCtx?.currentWeddingId) {
      alert('Please select a wedding first');
      return;
    }

    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      content: inputMessage,
      sender: 'user',
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInputMessage('');

    try {
      const response = await sendMessage(
        inputMessage,
        plannerCtx.currentWeddingId,
        plannerCtx.userId || 'default-user',
        (agentResponse) => {
          const agentMessage: ChatMessage = {
            id: `agent-${Date.now()}`,
            content: agentResponse.content,
            sender: 'agent',
            timestamp: new Date(),
            agent: agentResponse.agent
          };
          setMessages(prev => [...prev, agentMessage]);
        },
        (error) => {
          const errorMessage: ChatMessage = {
            id: `error-${Date.now()}`,
            content: `Error: ${error}`,
            sender: 'agent',
            timestamp: new Date(),
            agent: 'System'
          };
          setMessages(prev => [...prev, errorMessage]);
        }
      );

      if (response && !messages.some(m => m.content === response.content)) {
        const agentMessage: ChatMessage = {
          id: `agent-${Date.now()}`,
          content: response.content,
          sender: 'agent',
          timestamp: new Date(),
          agent: response.agent
        };
        setMessages(prev => [...prev, agentMessage]);
      }
    } catch (error) {
      console.error('Error sending message:', error);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const clearChat = () => {
    setMessages([]);
  };

  return (
    <Card title="Chat with Ella" className="h-[600px] flex flex-col">
      <div className="flex-1 overflow-y-auto space-y-4 p-4 bg-secondary/20 rounded-lg">
        {messages.length === 0 && (
          <div className="text-center text-muted-foreground py-8">
            <SparklesIcon className="w-12 h-12 mx-auto mb-4 text-primary/50" />
            <p>Start a conversation with Ella!</p>
            <p className="text-sm">Try asking about budget, vendors, timeline, or guests.</p>
          </div>
        )}
        
        {messages.map((message) => (
          <div
            key={message.id}
            className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
          >
            <div
              className={`max-w-[80%] p-3 rounded-lg ${
                message.sender === 'user'
                  ? 'bg-primary text-primary-foreground'
                  : 'bg-card border border-border'
              }`}
            >
              {message.sender === 'agent' && message.agent && (
                <div className="text-xs text-muted-foreground mb-1 font-medium">
                  {message.agent}
                </div>
              )}
              <div className="whitespace-pre-wrap">{message.content}</div>
              <div className="text-xs opacity-70 mt-1">
                {message.timestamp.toLocaleTimeString()}
              </div>
            </div>
          </div>
        ))}
        
        {isLoading && (
          <div className="flex justify-start">
            <div className="bg-card border border-border p-3 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-primary"></div>
                <span className="text-sm text-muted-foreground">Ella is thinking...</span>
              </div>
            </div>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>
      
      <div className="border-t border-border pt-4 space-y-3">
        <div className="flex space-x-2">
          <Input
            value={inputMessage}
            onChange={(e) => setInputMessage(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Ask Ella about your wedding plans..."
            className="flex-1"
            disabled={isLoading}
          />
          <Button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || isLoading}
            leftIcon={<PaperAirplaneIcon className="w-4 h-4" />}
          >
            Send
          </Button>
        </div>
        
        <div className="flex justify-between items-center">
          <div className="text-xs text-muted-foreground">
            {plannerCtx?.currentWedding?.weddingDetails?.coupleNames 
              ? `Planning for ${plannerCtx.currentWedding.weddingDetails.coupleNames}`
              : 'No wedding selected'
            }
          </div>
          <Button
            onClick={clearChat}
            variant="ghost"
            size="sm"
            className="text-xs"
          >
            Clear Chat
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default AgentChatInterface;
