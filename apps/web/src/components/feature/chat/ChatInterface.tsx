import Button from '@/src/components/common/Button';
import Card from '@/src/components/common/Card';
import Input from '@/src/components/common/Input';
import { PaperAirplaneIcon, SparklesIcon } from '@/src/components/icons/HeroIcons';
import { ELLA_AVATAR_URL, USER_AVATAR_URL } from '@/src/constants';
import { ChatMessage } from '@/src/types/index';
import Image from 'next/image';
import React, { KeyboardEvent, useEffect, useRef, useState } from 'react';


interface ChatInterfaceProps {
  messages: ChatMessage[];
  onSendMessage: (messageText: string) => Promise<void>;
  isLoadingInteraction: boolean;
  placeholder?: string;
  title?: string;
  showEllaAvatar?: boolean;
}

const ChatInterface: React.FC<ChatInterfaceProps> = ({
  messages,
  onSendMessage,
  isLoadingInteraction,
  placeholder = "Chat with Ella...",
  title = "Ella AI Assistant",
  showEllaAvatar = true,
}) => {
  const [inputText, setInputText] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(scrollToBottom, [messages]);

  useEffect(() => {
    if (!isLoadingInteraction && inputRef.current) {
      inputRef.current.focus();
    }
  }, [isLoadingInteraction]);

  const handleSend = async () => {
    if (inputText.trim() === '' || isLoadingInteraction) return;
    const textToSend = inputText;
    setInputText('');
    await onSendMessage(textToSend);
  };

  const handleKeyPress = (e: KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSend();
    }
  };

  return (
    <Card title={title} className="flex flex-col h-full !p-0">
      <div className="flex-grow overflow-y-auto p-4 space-y-4 bg-transparent rounded-t-lg custom-scrollbar">
        {messages.map((msg) => (
          <div key={msg.id} className={`flex ${msg.sender === 'user' ? 'justify-end' : msg.sender === 'system' ? 'justify-center' : 'justify-start'}`}>
            {msg.sender === 'system' ? (
              // System/status messages - centered and styled differently
              <div className="max-w-xs md:max-w-md lg:max-w-lg w-full">
                <div className="bg-muted/50 border border-border/50 rounded-lg px-4 py-3 text-center">
                  <div className="flex items-center justify-center mb-2">
                    <div className="w-6 h-6 rounded-full bg-primary/20 flex items-center justify-center mr-2">
                      <div className="w-2 h-2 rounded-full bg-primary animate-pulse"></div>
                    </div>
                    <span className="text-xs font-medium text-muted-foreground uppercase tracking-wide">Status</span>
                  </div>
                  <p className="text-sm text-muted-foreground whitespace-pre-wrap">{msg.text}</p>
                  <p className="text-xs opacity-50 mt-2">{new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</p>
                </div>
              </div>
            ) : (
              // Regular chat messages (user and ella)
              <div className={`flex items-end max-w-xs md:max-w-md lg:max-w-lg ${msg.sender === 'user' ? 'flex-row-reverse' : 'flex-row'}`}>
                {(msg.sender === 'ella' && showEllaAvatar) && <Image src={ELLA_AVATAR_URL} alt="Ella" width={32} height={32} className="w-8 h-8 rounded-full mr-2 self-start border-2 border-primary/50" />}
                {msg.sender === 'user' && <Image src={USER_AVATAR_URL} alt="User" width={32} height={32} className="w-8 h-8 rounded-full ml-2 self-start border-2 border-secondary/50" />}

                <div className={`px-4 py-3 rounded-xl shadow-md ${msg.sender === 'user' ? 'bg-primary text-primary-foreground rounded-br-none'
                  : 'bg-muted text-muted-foreground rounded-bl-none'
                  }`}>
                  {msg.imageUrl && <Image src={msg.imageUrl} alt="Generated content" width={400} height={300} className="my-2 rounded-lg max-w-full h-auto border border-border" />}
                  <p className="text-sm whitespace-pre-wrap">{msg.text}</p>
                  <p className="text-xs opacity-70 mt-1 text-right">{new Date(msg.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</p>
                </div>
              </div>
            )}
          </div>
        ))}
        {isLoadingInteraction && messages.length > 0 && messages[messages.length - 1].sender === 'user' && (
          <div className="flex justify-start">
            <div className="flex items-end max-w-xs md:max-w-md lg:max-w-lg flex-row">
              {showEllaAvatar && <Image src={ELLA_AVATAR_URL} alt="Ella" width={32} height={32} className="w-8 h-8 rounded-full mr-2 self-start border-2 border-primary/50" />}
              <div className="px-4 py-3 rounded-xl bg-secondary text-secondary-foreground rounded-bl-none shadow-md">
                <SparklesIcon className="w-5 h-5 animate-pulse text-primary inline-block mr-1" />
                <span className="italic text-sm">Ella is thinking...</span>
              </div>
            </div>
          </div>
        )}
        <div ref={messagesEndRef} />
      </div>
      <div className="p-4 border-t border-border bg-card/80 rounded-b-lg">
        <div className="flex items-center space-x-2">
          <Input
            ref={inputRef}
            type="text"
            value={inputText}
            onChange={(e) => setInputText(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder={placeholder}
            className="flex-grow !bg-input focus:!ring-primary focus:!border-primary"
            disabled={isLoadingInteraction}
          />
          <Button
            onClick={handleSend}
            disabled={isLoadingInteraction || inputText.trim() === ''}
            variant="primary"
            className="!px-3"
            aria-label="Send message"
          >
            {isLoadingInteraction ? (
              <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
            ) : (
              <PaperAirplaneIcon className="h-5 w-5" />
            )}
          </Button>
        </div>
      </div>
    </Card>
  );
};

export default ChatInterface;
