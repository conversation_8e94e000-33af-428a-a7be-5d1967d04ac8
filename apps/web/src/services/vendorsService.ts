import { supabaseData } from "@/src/services/supabaseDataClient"

// Types for the vendor database tables
export interface DatabaseVendor {
  id: number
  business_name: string
  description: string
  city: string
  state: string
  website: string
  phone: string
  email: string
  address: string
  zip_code?: string
  facebook?: string
  instagram?: string
  twitter?: string
  linkedin?: string
  pinterest?: string
  youtube?: string
  review_score?: number
  review_count?: number
  pricing?: any // JSONB field
  photo_gallery?: string[]
  stage_1_complete?: boolean
  stage_2_complete?: boolean
  created_at?: string
  updated_at?: string
}

export type VendorTableName =
  | "venues"
  | "photographers"
  | "florists"
  | "bands"
  | "bar_services"
  | "beauty"
  | "bridal_salons"
  | "cakes"
  | "caterers"
  | "dance_lessons"
  | "decor"
  | "djs"
  | "ensembles_soloists"
  | "favors_gifts"
  | "invitations_paper"
  | "jewelers"
  | "officiants_counseling"
  | "photo_booths"
  | "rehearsal_dinners_parties"
  | "rentals"
  | "transportation"
  | "travel_specialists"
  | "videographers"
  | "wedding_planners"

export interface VendorSearchParams {
  search?: string
  city?: string
  state?: string
  limit?: number
  offset?: number
}

// Map application vendor categories to database table names
export const categoryToTableMap: Record<string, VendorTableName> = {
  Venue: "venues",
  Photographer: "photographers",
  Videographer: "videographers",
  Caterer: "caterers",
  Florist: "florists",
  "DJ/Band": "bands", // Could also map to 'djs' or 'ensembles_soloists'
  "Hair & Makeup": "beauty",
  Cake: "cakes",
  Officiant: "officiants_counseling",
  "Wedding Planner": "wedding_planners",
  Transportation: "transportation",
  Attire: "bridal_salons",
  Stationery: "invitations_paper",
  Favors: "favors_gifts",
  Other: "venues", // Default fallback
}

export class VendorsService {
  // Search vendors by category
  static async searchVendorsByCategory(
    category: string,
    params: VendorSearchParams = {}
  ): Promise<DatabaseVendor[]> {
    const tableName = categoryToTableMap[category] || "venues"
    const { search, city, state, limit = 20, offset = 0 } = params

    let query = supabaseData
      .from(tableName)
      .select("*")
      .eq("stage_1_complete", true) // Only show completed vendor profiles
      .range(offset, offset + limit - 1)

    // Add search filters
    if (search) {
      query = query.or(
        `business_name.ilike.%${search}%,description.ilike.%${search}%`
      )
    }

    if (city) {
      query = query.ilike("city", `%${city}%`)
    }

    if (state) {
      query = query.ilike("state", `%${state}%`)
    }

    // Order by review score and count
    query = query
      .order("review_score", { ascending: false })
      .order("review_count", { ascending: false })

    const { data, error } = await query

    if (error) {
      console.error("Error fetching vendors:", error)
      throw error
    }

    return data || []
  }

  // Get all available vendor categories
  static async getAvailableCategories(): Promise<
    { category: string; table: VendorTableName; count: number }[]
  > {
    const categories = []

    for (const [category, tableName] of Object.entries(categoryToTableMap)) {
      try {
        const { count, error } = await supabaseData
          .from(tableName)
          .select("*", { count: "exact", head: true })
          .eq("stage_1_complete", true)

        if (!error && count !== null) {
          categories.push({ category, table: tableName, count })
        }
      } catch (error) {
        console.warn(`Could not get count for ${category}:`, error)
      }
    }

    return categories.sort((a, b) => b.count - a.count)
  }

  // Get specific vendor by ID from a specific table
  static async getVendorById(
    tableName: VendorTableName,
    id: number
  ): Promise<DatabaseVendor | null> {
    const { data, error } = await supabaseData
      .from(tableName)
      .select("*")
      .eq("id", id)
      .single()

    if (error) {
      console.error("Error fetching vendor:", error)
      return null
    }

    return data
  }

  // Search across all vendor tables
  static async searchAllVendors(params: VendorSearchParams = {}): Promise<
    {
      category: string
      vendors: DatabaseVendor[]
    }[]
  > {
    const results = []

    for (const category of Object.keys(categoryToTableMap)) {
      try {
        const vendors = await this.searchVendorsByCategory(category, {
          ...params,
          limit: params.limit || 5, // Limit results per category for "all" search
        })

        if (vendors.length > 0) {
          results.push({ category, vendors })
        }
      } catch (error) {
        console.warn(`Error searching ${category}:`, error)
      }
    }

    return results
  }
}
